.shadowBg {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 997;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

.dialogNoupou {
  width: 624px;
  height: 548px;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 999;
  transform: translate(-50%, -50%);
  background: url('../../assets/images/index/dialogBg.png') no-repeat;
  background-size: 100% 100%;

  padding: 56px 28px 0 28px;
  box-sizing: border-box;

  .hint {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 40px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 56px;
    text-align: left;
    font-style: normal;
  }

  .use {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 28px;
    color: #7c2303;
    line-height: 40px;
    text-align: left;
    font-style: normal;
  }

  .noupon {
    margin-top: 42px;
    width: 100%;
    height: 160px;
    background: url('../../assets/images/index/conpouBg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;

    .leftItem {
      width: 30%;
      height: 100%;
      position: relative;

      .content {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        text-align: center;

        .moneyCount {
          margin-top: 30px;
          height: 60px;
          line-height: 60px;
          font-size: 45px;
          font-weight: 700;
          font-family: LiGothicMed;
          color: #e60f37;

          .unit {
            display: inline;
            font-size: 28px;
          }
        }

        .xianzhi {
          font-family:
            PingFangSC,
            PingFang SC;
          font-size: 20px;
          color: #e60f37;
        }
      }
    }
    .rightItem {
      width: 70%;
      padding: 22px 0 0 30px;
      box-sizing: border-box;
      position: relative;
      font-size: 20px;

      .title {
        padding-top: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 34px;
      }

      .time {
        color: #e40032;
        margin-bottom: 10px;

        .timeText {
          display: inline;
          color: #ccc;
        }
      }

      .useTitle {
        color: #ccc;
      }
    }
  }

  .btn {
    margin: 52px auto;
    width: 518px;
    height: 96px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 40px;
    color: #fffffa;
    line-height: 96px;
    text-align: center;
    font-style: normal;
    background: url('../../assets/images/index/btnBG.png') no-repeat;
    background-size: 100% 100%;
    margin-bottom: 86px;
  }

  .closeBtn {
    margin: 0 auto;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    border: 4px solid #ffffff;
    box-sizing: border-box;
    color: #fff;
    text-align: center;
    line-height: 36px;
    font-size: 20px;
    font-weight: 700;
  }
}

.dialogNoupou-notGet {
  width: 590px;
  height: 548px;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 999;
  transform: translate(-50%, -50%);
  background: url('../../assets/images/index/dialogBg.png') no-repeat;
  background-size: 100% 100%;

  padding: 34px 28px 0 28px;
  box-sizing: border-box;

  .moneyValueIcon {
    position: absolute;
    top: -112px;
    right: -45px;
    width: 354px;
    height: 264px;
    background: url('../../assets/images/index/188.png') no-repeat;
    background-size: 100% 100%;
  }

  .coloured {
    position: absolute;
    z-index: -1;
    top: 75px;
    left: 140px;
    width: 138px;
    height: 26px;
    background: url('../../assets/images/index/slice.png') no-repeat;
    background-size: 100% 100%;
  }

  .hint {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 40px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 56px;
    text-align: left;
    font-style: normal;
  }

  .use {
    margin-top: 5px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 28px;
    color: #7c2303;
    line-height: 40px;
    text-align: left;
    font-style: normal;
  }

  .noupon {
    margin-top: 32px;
    width: 100%;
    height: 160px;
    background: url('../../assets/images/index/conpouBg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;

    .leftItem {
      width: 30%;
      height: 100%;
      position: relative;

      .content {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        text-align: center;

        .moneyCount {
          margin-top: 30px;
          height: 60px;
          line-height: 60px;
          font-size: 45px;
          font-weight: 700;
          font-family: LiGothicMed;
          color: #e60f37;

          .unit {
            display: inline;
            font-size: 28px;
          }
        }

        .xianzhi {
          font-family:
            PingFangSC,
            PingFang SC;
          font-size: 20px;
          color: #e60f37;
        }
      }
    }
    .rightItem {
      width: 70%;
      padding: 22px 0 0 30px;
      box-sizing: border-box;
      position: relative;
      font-size: 20px;

      .title {
        padding-top: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 34px;
      }

      .time {
        color: #e40032;
        margin-bottom: 10px;

        .timeText {
          display: inline;
          color: #ccc;
        }
      }

      .useTitle {
        color: #ccc;
      }
    }
  }
}

.juxing {
  position: fixed;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 626px;
  height: 582px;
  background: url('../../assets/images/index/juxing.png') no-repeat;
  background-size: 100% 100%;

  .juxing_btn {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    width: 518px;
    height: 96px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 40px;
    color: #fffffa;
    line-height: 96px;
    text-align: center;
    font-style: normal;
    background: url('../../assets/images/index/btnBG.png') no-repeat;
    background-size: 100% 100%;
    margin-bottom: 46px;
  }

  .closeBtn_dialog {
    position: fixed;
    bottom: -80px;
    left: 50%;
    transform: translate(-50%, 0);
    margin: 0 auto;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    border: 4px solid #ffffff;
    box-sizing: border-box;
    color: #fff;
    text-align: center;
    line-height: 36px;
    font-size: 20px;
    font-weight: 700;
  }
}

.reveiceCoupon {
  width: 680px;
  height: 127px;
  background: linear-gradient(105deg, #faeff2 0%, #fbd8e8 31%, #f7dbe2 55%, #f6ded8 100%);
  box-shadow: inset 0px 2px 6px 0px rgba(255, 255, 255, 0.9);
  border-radius: 24px 24px 0px 0px;
  margin: 0 auto 30px;
  padding: 18px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .leftIcon {
    width: 86px;
    height: 90px;
    background: url('../../assets/images/my/my_188.png') no-repeat;
    background-size: 100% 100%;
  }

  .center {
    width: 400px;

    .topTitle {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 700;
      font-size: 28px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 48px;
      text-align: left;
      font-style: normal;
    }

    .bottomXianZhi {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 20px;
      color: #7c2303;
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
  }

  .rightBtn {
    width: 124px;
    height: 52px;
    background: linear-gradient(220deg, #ff6a5e 0%, #e40633 100%), linear-gradient(220deg, #ff6a5e 0%, #e40633 100%);
    border-radius: 16px;

    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 20px;
    color: #ffffff;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    line-height: 52px;
  }
}
