import { memo, useEffect, useMemo } from 'react'
import Taro from '@tarojs/taro'
import { Message } from '@/store/chat'
import txImg from '@/assets/images/chat/tx.png'
import { useAsyncFn } from 'react-use'
import { Loading } from '@taroify/core'

export const MessageCanvas = memo(({ message, threadId = '' }: { message: Message; threadId: string | null }) => {
  console.log('message', message)
  const imgs = message.tool_calls || []

  const data = useMemo(() => {
    let content = message.content
    // 如果内容中包含 \n 字符序列，将其替换为真实的换行符
    if (content && content.includes('\\n')) {
      content = content.replace(/\\n/g, '\n')
    }
    const isHuman = message.type === 'human'
    const containsImage = /!\[.*?\]\(.*?\)/.test(message.content) // 检测 Markdown 是否包含图片
    return { processedContent: content, isHuman, containsImage }
  }, [message])

  const [previewState, previewFetch] = useAsyncFn(async () => {
    let res
    try {
      res = await Taro.request({
        url: `${process.env.TARO_APP_API_AI}/server/v1/genimage/preview`,
        method: 'POST',
        data: message.tool_calls![0].args.canvas
      })
    } catch (error) {
      return null
    }
    return res.data.output
  }, [])

  useEffect(() => {
    previewFetch()
  }, [])
  console.log('previewState', previewState)
  return (
    <div className={`flex rounded-[20px] ${data.isHuman ? 'justify-end mx-[20px]' : 'justify-start'} mb-2`}>
      <div
        className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto ${
          data.isHuman ? 'bg-[#000000] text-white' : 'text-black'
        } ${data.isHuman && data.containsImage ? 'flex flex-col items-end' : ''}`} // 添加样式
      >
        <div className="py-[10px] text-[28px] rounded-lg w-auto">{message.content}</div>

        <div className="bg-[#EFEFF2] w-[500px] h-[500px] rounded-[16px] relative">
          {previewState.loading ? (
            <div className="w-full h-full absolute left-0 top-0 z-10 flex_center">
              <Loading />
            </div>
          ) : null}
          <img className="w-[500px] h-[500px]" src={previewState.value || message.preview || imgs[0].args.canvas?.view?.url} alt="" />
        </div>

        <div
          className="my-[20px] w-[176px] h-[72px] rounded-[16px] flex_center bg-[#F8F8F8]"
          onClick={() => {
            Taro.navigateTo({
              url: `/pages/canvas/index?threadId=${threadId}&messageId=${message.id}`
            })
          }}
        >
          <img className="w-[42px] h-[40px]" src={txImg} alt="" />
          <div className="flex items-center font-normal text-[24px] text-black leading-[24px] text-left not-italic">马上设计</div>
        </div>
      </div>
    </div>
  )
})
