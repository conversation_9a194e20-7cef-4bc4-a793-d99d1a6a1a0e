import dayjs from 'dayjs'
import './UseConpouItem.scss'

const ConputItem = (props: any) => {
  return (
    <>
      <div className="coupon">
        <div className="useCouponItem">
          <div className="leftItem">
            <div className="count">x{props.conpouInfo?.reduceNum}</div>
            <div className="content">
              <div className="moneyCount">
                {(props.conpouInfo?.reducePrice || 0) / 100}
                <span className="unit">元</span>
              </div>
              <div className="xianzhi">{props.conpouInfo?.couponName}</div>
            </div>
          </div>
          <div className="rightItem">
            <div className="leftContentBox">
              <div className="title">{props.conpouInfo?.couponName}</div>
              <div className="time">
                {dayjs(props.conpouInfo?.endTime - props.conpouInfo?.startTime).format('HH : mm : ss')}{' '}
                <span className="timeText">后过期</span>
              </div>
              <div className="useTitle">满{props.conpouInfo?.fullPrice / 100}金额可用</div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ConputItem
