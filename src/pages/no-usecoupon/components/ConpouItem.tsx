import dayjs from 'dayjs'
import './ConpouItem.scss'

const ConputItem = (props: any) => {
  return (
    <>
      <div className="noUseCoupon">
        <div className="noUseCouponItem">
          <div className="leftItem">
            <div className="count">x{props.data.totalNum}</div>
            <div className="content">
              <div className="moneyCount">
                {props.data.reducePrice / 100}
                <span className="unit">元</span>
              </div>
              <div className="xianzhi">{props.data.couponName}</div>
            </div>
          </div>
          <div className="rightItem">
            <div className="leftContentBox">
              <div className="title">{props.data.couponName}</div>
              <div className="time">{dayjs(props.data.endTime - props.data.startTime).format('HH : mm : ss')} 后过期</div>
              <div className="useTitle">满{props.data.fullPrice / 100}金额可用</div>
            </div>
            <div className="rightBtnBox">
              <div className="title">{props.state}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ConputItem
