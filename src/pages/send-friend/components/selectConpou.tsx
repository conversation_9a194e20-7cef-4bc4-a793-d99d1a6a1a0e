import dayjs from 'dayjs'
import { useState } from 'react'
import './selectConpou.scss'

const Index = (props) => {
  const [defaultSelect, setDefaultSelect] = useState(props.couponId)

  const selectCurrentConpouItem = (item) => {
    props.updateSelectConpouItem(item)
    setDefaultSelect(item.id)
  }
  return (
    <>
      <div className="selectDialog">
        <div className="header">
          <div className="title">选择代金券</div>
          <div className="closeBtn" onClick={() => props.updateVisible(false)}>
            ×
          </div>
        </div>
        <div className="body">
          {props.nouponList.map((item) => {
            return (
              <>
                <div className="couponItem">
                  <div className="leftItem">
                    <div className="count">x{item.totalNum}</div>
                    <div className="content">
                      <div className="moneyCount">
                        {item.reducePrice / 100}
                        <span className="unit">元</span>
                      </div>
                      <div className="xianzhi">{item.couponName}</div>
                    </div>
                  </div>
                  <div className="rightItem">
                    <div className="leftContentBox">
                      <div className="title">{item.couponName}</div>
                      <div className="time">
                        {dayjs(item.endTime - item.startTime).format('HH : ss : mm')} <span className="timeText">后过期</span>
                      </div>
                      <div className="useTitle">满{item.fullPrice / 100}金额可用</div>
                    </div>
                    <div className="rightBtnBox">
                      <div className="btns">
                        {defaultSelect == item.id ? (
                          <div className="active">已选择</div>
                        ) : (
                          <div className="btn" onClick={() => selectCurrentConpouItem(item)}>
                            选择
                          </div>
                        )}
                      </div>
                      {/* <div className="text">已选择</div> */}
                    </div>
                  </div>
                </div>
              </>
            )
          })}
        </div>
      </div>
    </>
  )
}

export default Index
